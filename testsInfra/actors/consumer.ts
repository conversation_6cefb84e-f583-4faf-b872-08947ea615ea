import { expect, Page } from '@playwright/test';
import DataManager from '../dataManager';
import CreditCard from '../entities/creditCard';
import Package from '../entities/package';
import Site from '../entities/site';
import DropTypesEnum from '../enums/dropTypesEnum';
import ConsumerCheckoutActions from '../innerInfra/actorActions/consumerActions/consumerCheckoutActions';
import ConsumerLoginAndRegisterActions from '../innerInfra/actorActions/consumerActions/consumerLoginAndRegisterActions';
import HomeConsumerActions from '../innerInfra/actorActions/consumerActions/homeConsumerActions';
import IntercomActions from '../innerInfra/actorActions/intercomActions';
import { TestStep } from '../innerInfra/decorators';
import { Store } from '../innerInfra/interfaces/store';
import HomeConsumerPage from '../innerInfra/pages/consumerPages/homeConsumerPage';
import SettingsPage from '../innerInfra/pages/consumerPages/settingsPage';
import TrackerConsumerPage from '../innerInfra/pages/consumerPages/trackerConsumerPage';
import { DroppingUserActor } from './droppingUser';

export class ConsumerUser extends DroppingUserActor {
  readonly name: string;
  readonly email: string;
  readonly phoneNumber: string;
  readonly creditCard?: CreditCard;

  private _droppedPackages: Package[];
  private readonly checkoutActions: ConsumerCheckoutActions;
  private readonly homeActions: HomeConsumerActions;
  private readonly intercomActions: IntercomActions;
  private _shouldPayForDropPass: boolean;
  private readonly loginAndRegisterActions: ConsumerLoginAndRegisterActions;

  constructor(
    page: Page,
    name: string,
    phoneNumber: string,
    site?: Site,
    options?: {
      address?: string;
      dropType?: DropTypesEnum;
      packages?: Package[];
      creditCard?: CreditCard;
      email?: string;
    },
  ) {
    super(page, options?.dropType ?? DropTypesEnum.Collection, options?.address ?? 'Dummer', site);

    this.name = name;
    this.phoneNumber = phoneNumber;
    this._droppedPackages = [];
    this.creditCard = options?.creditCard;
    this.checkoutActions = new ConsumerCheckoutActions(this);
    this.homeActions = new HomeConsumerActions(this);
    this.intercomActions = new IntercomActions(this);
    this.loginAndRegisterActions = new ConsumerLoginAndRegisterActions(this);
    this.email = options?.email ?? DataManager.Consts.AUTOMATION_CONSUMER_DEFAULT_EMAIL;
    this._shouldPayForDropPass = true;
  }

  private addPackagesToConsumer(packages: Package[]) {
    this._droppedPackages.push(...packages);
  }

  private setConsumerTo_Not_PayForNextDropPass() {
    this._shouldPayForDropPass = false;
  }

  private setConsumerToPayForNextDropPass() {
    this._shouldPayForDropPass = true;
  }

  get shouldPayForDropPass() {
    return this._shouldPayForDropPass;
  }

  get droppedPackages(): Package[] {
    return [...this._droppedPackages];
  }

  override get perform() {
    return {
      ...super.perform,
      scanStoreBarcodeThroughApp: this.bindAction(this.homeActions.scanStoreBarcodeThroughApp, this.homeActions),
      addPackages: this.bindAction(this.checkoutActions.addPackages, this.checkoutActions),
      addPromoCode: this.bindAction(this.checkoutActions.addPromoCode, this.checkoutActions),
      addReceiptsManually: this.bindAction(this.checkoutActions.addReceiptsManually, this.checkoutActions),
      fillPaymentDetails: this.bindAction(this.checkoutActions.fillPaymentDetails, this.checkoutActions),
      checkout: this.bindAction(this.checkoutActions.checkout, this.checkoutActions),
      addAddress: this.bindAction(this.checkoutActions.addAddress, this.checkoutActions),
      selectDeliveryMethod: this.bindAction(this.checkoutActions.selectDeliveryMethod, this.checkoutActions),
      selectCollectionType: this.bindAction(this.checkoutActions.selectCollectionType, this.checkoutActions),
      dropPackages: this.bindAction(this.checkoutActions.dropPackages, this.checkoutActions),
      selectDropitPass: this.bindAction(this.checkoutActions.selectDropitPass, this.checkoutActions),
      finishDropping: this.bindAction(this.homeActions.finishDropping, this.homeActions),
      login: this.bindAction(this.loginAndRegisterActions.login, this.loginAndRegisterActions),
      register: this.bindAction(this.loginAndRegisterActions.register, this.loginAndRegisterActions),
      confirmCutoffTimePopup: this.bindAction(this.checkoutActions.confirmCutoffTimePopup, this.checkoutActions),
      existingConsumerDroppingAPackage: this.bindAction(this.existingConsumerDroppingAPackage, this),
      inputPhoneNumber: this.bindAction(this.loginAndRegisterActions.inputPhoneNumber, this.loginAndRegisterActions),
      startConversationWithIntercom: this.bindAction(
        this.intercomActions.startConversationWithIntercom,
        this.intercomActions,
      ),
      logout: this.bindAction(this.logout, this),
    };
  }

  override get ask() {
    return {
      ...super.ask,
      whatIsThePaymentErrorText: this.bindAction(this.checkoutActions.whatIsThePaymentErrorText, this.checkoutActions),
      whatIsTheLoginErrorMessage: this.bindAction(
        this.loginAndRegisterActions.whatIsTheLoginErrorMessage,
        this.loginAndRegisterActions,
      ),
    };
  }

  override get assert() {
    return {
      ...super.assert,
      invalidPhoneNumbers: this.bindAction(this.assertInvalidPhoneNumbers, this),
      invalidVerificationCodes: this.bindAction(this.assertInvalidVerificationCodes, this),
      intercomConversation: this.bindAction(this.intercomActions.assertIntercomConversation, this.intercomActions),
    };
  }

  override get goTo() {
    return {
      ...super.goTo,
      loginPage: this.bindAction(this.loginAndRegisterActions.goToLoginPage, this.loginAndRegisterActions),
      verificationCodePage: this.bindAction(
        this.loginAndRegisterActions.goToVerificationCodePage,
        this.loginAndRegisterActions,
      ),
      registerPage: this.bindAction(this.loginAndRegisterActions.goToRegisterPage, this.loginAndRegisterActions),
      homePage: this.bindAction(this.homeActions.goToHomePage, this.homeActions),
      checkoutPage: this.bindAction(this.checkoutActions.goToCheckoutPage, this.checkoutActions),
      trackerPage: this.bindAction(this.goToTrackOrdersPage, this),
      settingsPage: this.bindAction(this.goToSettingsPage, this),
    };
  }

  get set() {
    return {
      consumerTo_Not_PayForNextDropPass: this.bindAction(this.setConsumerTo_Not_PayForNextDropPass, this),
      consumerToPayForNextDropPass: this.bindAction(this.setConsumerToPayForNextDropPass, this),
      addPackagesToConsumer: this.bindAction(this.addPackagesToConsumer, this),
    };
  }

  get get() {
    return {
      ...super.get,
      phoneNumber: this.phoneNumber,
      name: this.name,
      shouldPayForDropPass: this._shouldPayForDropPass,
      droppedPackages: this._droppedPackages,
      creditCard: this.creditCard,
    };
  }

  protected override async enterVerificationCode({
    verificationCode,
    navigateToPageIfNeeded = false,
  }: {
    verificationCode: string;
    navigateToPageIfNeeded?: boolean;
  }) {
    await this.loginAndRegisterActions.enterVerificationCode({
      verificationCode,
      navigateToPageIfNeeded,
    });
  }

  protected override async whatIsTheVerificationCodeErrorMessage() {
    return this.loginAndRegisterActions.whatIsTheVerificationCodeErrorMessage();
  }

  protected whatIsThePackageInputScanError() {
    return this.checkoutActions.whatIsThePackageInputScanError();
  }

  protected getHomePage() {
    return new HomeConsumerPage(this.page);
  }

  @TestStep(`Enter package code`)
  protected override async enterPackageCode(
    packagee: Package,
    { shouldPackageAdditionFail }: { shouldPackageAdditionFail: boolean },
  ) {
    const addedPackages = await this.perform.addPackages({
      additionalPackages: [packagee],
      shouldBarcodeInputErrorAppear: shouldPackageAdditionFail,
      shouldNavigateToPage: false,
    });

    if (addedPackages.length > 0) {
      this.set.addPackagesToConsumer(addedPackages);
    }
  }

  @TestStep(`Existing Consumer logs in and drops their package(s)`)
  private async existingConsumerDroppingAPackage({
    store = this.site.stores[0],
    shouldNavigateToPage = false,
    promoCode,
    money,
    additionalContactInfo,
    failDrop = false,
    expectedAmountToPay = 0,
    shouldLogin = true,
    packages = [],
    shouldFinishDropping = true,
  }: {
    store?: Store;
    shouldNavigateToPage?: boolean;
    promoCode?: string;
    money?: string;
    additionalContactInfo?: string;
    failDrop?: boolean;
    expectedAmountToPay?: number;
    shouldLogin?: boolean;
    packages?: Package[];
    shouldFinishDropping?: boolean;
  } = {}) {
    if (shouldLogin) {
      await this.perform.login();
    }

    await this.perform.scanStoreBarcodeThroughApp({ store, shouldNavigateToPage });
    await this.perform.dropPackages({
      shouldNavigateToPage: false,
      promoCode,
      packages,
      money,
      additionalContactInfo,
      failDrop,
      expectedAmountToPay,
    });

    if (!failDrop && shouldFinishDropping) {
      await this.perform.finishDropping();
    }
  }

  @TestStep(`Assert invalid phone numbers`)
  private async assertInvalidPhoneNumbers({
    invalidPhoneNumbers,
    assertion,
    countryCode = 'Israel',
  }: {
    invalidPhoneNumbers: string[];
    assertion: (errorMessage: string, phoneNumber: string) => void;
    countryCode?: string;
  }) {
    for (const phoneNumber of invalidPhoneNumbers) {
      const consumerLoginPage = await this.perform.inputPhoneNumber({
        phoneNumber,
        country: countryCode,
        needToNavigateToPage: false,
      });

      const errorMessage = await this.ask.whatIsTheLoginErrorMessage();

      const borderColor = await consumerLoginPage.phoneNumberField.getBorderColor();
      expect.soft(borderColor, 'Asserting Phone number input border color').toBe(DataManager.Consts.ERROR_COLOR);
      assertion(errorMessage, phoneNumber);
    }
  }

  @TestStep(`Assert invalid verification codes`)
  protected async assertInvalidVerificationCodes(
    invalidCodes: string[],
    assertion: (errorMessage: string, code: string) => void,
  ) {
    for (const code of invalidCodes) {
      await this.perform.enterVerificationCode({
        verificationCode: code,
        navigateToPageIfNeeded: false,
      });

      const errorMessage = await this.ask.whatIsTheVerificationCodeErrorMessage();
      assertion(errorMessage, code);
    }
  }

  private async goToSettingsPage({ shouldNavigateThroughUI = true }: { shouldNavigateThroughUI?: boolean } = {}) {
    let settingsPage = new SettingsPage(this.page);

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: settingsPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        const homePage = new HomeConsumerPage(this.page);

        await homePage.menuButton.click();
        await homePage.settingsButton.click();
      },
    });

    settingsPage = new SettingsPage(this.page);
    await settingsPage.languageSelector.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 5000,
      errorMessage: 'Failed to load settings page, language selector is not visible',
    });

    return settingsPage;
  }

  private async goToTrackOrdersPage({ shouldNavigateThroughUI = true }: { shouldNavigateThroughUI?: boolean } = {}) {
    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: new TrackerConsumerPage(this.page),
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        const homePage = new HomeConsumerPage(this.page);

        await homePage.menuButton.click();
        await homePage.trackOrdersButton.click();
      },
    });

    const trackerPage = new TrackerConsumerPage(this.page);
    await trackerPage.searchInput.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 5000,
      errorMessage: 'Failed to load track orders page, search input is not visible',
    });

    return trackerPage;
  }

  @TestStep(`Logout`)
  private async logout() {
    const homePage = new HomeConsumerPage(this.page);

    await homePage.menuButton.click();
    await homePage.logoutButton.click();
  }
}
