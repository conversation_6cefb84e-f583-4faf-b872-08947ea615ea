import { expect, Page } from '@playwright/test';
import DataManager from '../dataManager';
import { LoginType } from '../entities/loginType';
import Package from '../entities/package';
import Site from '../entities/site';
import DeliveryDetailsActions from '../innerInfra/actorActions/adminActions/deliveryDetailsActions';
import OperationHubActions from '../innerInfra/actorActions/adminActions/operationHubActions';
import IntercomActions from '../innerInfra/actorActions/intercomActions';
import LoginActions from '../innerInfra/actorActions/loginActions';
import { TestStep } from '../innerInfra/decorators';
import { Auth0LoginUser } from '../innerInfra/interfaces/auth0Login';
import DeliveriesPage from '../innerInfra/pages/businessPages/hfs/deliveriesPage';
import OperationHubPage from '../innerInfra/pages/businessPages/hfs/operationsHub';
import StoreDetailsPage from '../innerInfra/pages/businessPages/hfs/storeDetailsPage';
import StoresViewPage from '../innerInfra/pages/businessPages/hfs/storesViewPage';
import UpcomingPickupsPage from '../innerInfra/pages/businessPages/hfs/upcomingPickupsPage';
import VendorDetailsPage from '../innerInfra/pages/businessPages/hfs/vendorDetailsPage';
import VendorsViewPage from '../innerInfra/pages/businessPages/hfs/vendorsViewPage';
import NavigationBusinessPage from '../innerInfra/pages/businessPages/navigationBusinessPage';
import IntercomModal from '../innerInfra/pages/intercomModal';
import BaseActor from './baseActor';

export default class AdminUser extends BaseActor implements Auth0LoginUser {
  readonly email: string;
  readonly password: string;
  readonly loginType: LoginType = DataManager.Consts.LoginTypes.Business;

  private readonly operationHubActions: OperationHubActions;
  private readonly deliveryDetailsActions: DeliveryDetailsActions;
  private readonly loginActions: LoginActions<AdminUser>;
  private readonly intercomActions: IntercomActions;

  constructor(
    page: Page,
    email: string = DataManager.Consts.BUSINESS_ADMIN_EMAIL,
    password: string = DataManager.Consts.BUSINESS_ADMIN_PASSWORD,
  ) {
    if (!email || !password) {
      throw new Error('Email and password cannot be undefined');
    }

    super(page);

    this.email = email;
    this.password = password;
    this.operationHubActions = new OperationHubActions(this);
    this.deliveryDetailsActions = new DeliveryDetailsActions(this);
    this.loginActions = new LoginActions<AdminUser>(this);
    this.intercomActions = new IntercomActions(this);
  }

  override get perform() {
    return {
      ...super.perform,
      login: this.bindAction(this.loginActions.login, this.loginActions),
      goToOperationsHubForTheSite: this.bindAction(this.goToOperationsHubForTheSite, this),
      receivePackages: this.bindAction(this.operationHubActions.receivePackages, this.operationHubActions),
      handoverPackages: this.bindAction(this.operationHubActions.handoverDelivery, this.operationHubActions),
      searchPackage: this.bindAction(this.operationHubActions.searchPackage, this.operationHubActions),
      preparePackageForDispatch: this.bindAction(
        this.operationHubActions.prepareDeliveryForDispatch,
        this.operationHubActions,
      ),
      dispatchDelivery: this.bindAction(this.operationHubActions.dispatchDelivery, this.operationHubActions),
      loginAndReceivePackages: this.bindAction(
        this.operationHubActions.loginAndReceivePackages,
        this.operationHubActions,
      ),
      prepareAndDispatchDelivery: this.bindAction(
        this.operationHubActions.prepareAndDispatchDelivery,
        this.operationHubActions,
      ),
      markDeliveryAsDelivered: this.bindAction(
        this.deliveryDetailsActions.markDeliveryAsDelivered,
        this.deliveryDetailsActions,
      ),
      sealPackage: this.bindAction(this.deliveryDetailsActions.sealPackage, this.deliveryDetailsActions),
      deliverPackages: this.bindAction(this.deliveryDetailsActions.deliverPackages, this.deliveryDetailsActions),
      startConversationWithIntercom: this.bindAction(
        this.intercomActions.startConversationWithIntercom,
        this.intercomActions,
      ),
    };
  }

  override get ask() {
    return {
      ...super.ask,
      whatIsTheLoginErrorMessage: this.bindAction(this.loginActions.whatIsTheLoginErrorMessage, this.loginActions),
      getErrorPopupMessageForEmptyField: this.bindAction(
        this.loginActions.getErrorPopupMessageForEmptyField,
        this.loginActions,
      ),
      whatIsTheDeliveryStatus: this.bindAction(
        this.deliveryDetailsActions.whatIsTheDeliveryStatus,
        this.deliveryDetailsActions,
      ),
    };
  }

  override get assert() {
    return {
      invalidLoginInputs: this.bindAction(this.loginActions.assertInvalidLoginInputs, this.loginActions),
      deliveryStatus: this.bindAction(this.assertDeliveryStatus, this),
      intercomConversation: this.bindAction(this.intercomActions.assertIntercomConversation, this.intercomActions),
    };
  }

  override get goTo() {
    return {
      loginPage: this.bindAction(this.loginActions.goToLoginPage, this.loginActions),
      intercomModal: this.bindAction(this.openIntercomModal, this),
      operationsHubPage: this.bindAction(this.goToOperationsHubForTheSite, this),
      pickupsPage: this.bindAction(this.goToPickupsPage, this),
      deliveriesPage: this.bindAction(this.goToDeliveriesPage, this),
      storesViewPage: this.bindAction(this.goToStoresViewPage, this),
      vendorsViewPage: this.bindAction(this.goToVendorsViewPage, this),
    };
  }

  @TestStep('Go to pickups page')
  private async goToPickupsPage({
    shouldNavigateToPage = true,
    shouldNavigateThroughUI = true,
  }: { shouldNavigateToPage?: boolean; shouldNavigateThroughUI?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    const upcomingStorePackagesResponse = this.page.waitForResponse(
      (response) => response.url().includes('/upcoming-store-packages/') && response.status() === 200,
    );

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        await navigationBusinessPage.pickupsButton.click();
      },
    });

    const pickupsPage = new UpcomingPickupsPage(this.page);
    await pickupsPage.storeFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load pickups page, search bar is not visible',
    });

    const response = await upcomingStorePackagesResponse;
    expect(response.status(), 'Failed to load pickups, response status is not 200').toBe(200);

    return pickupsPage;
  }

  @TestStep('Go to deliveries page')
  private async goToDeliveriesPage({
    shouldNavigateToPage = true,
    shouldNavigateThroughUI = true,
  }: { shouldNavigateToPage?: boolean; shouldNavigateThroughUI?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    const upcomingStorePackagesResponse = this.page.waitForResponse(
      (response) => response.url().includes('/upcoming-deliveries/') && response.status() === 200,
    );

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        await navigationBusinessPage.deliveriesButton.click();
      },
    });

    const deliveriesPage = new DeliveriesPage(this.page);
    await deliveriesPage.deliveryIdFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load deliveries page, search bar is not visible',
    });

    const response = await upcomingStorePackagesResponse;
    expect(response.status(), 'Failed to load deliveries, response status is not 200').toBe(200);

    return deliveriesPage;
  }

  @TestStep('Go to stores view page')
  private async goToStoresViewPage({
    shouldNavigateToPage = false,
    viewRandomStoreDetails = false,
  }: { shouldNavigateToPage?: boolean; viewRandomStoreDetails?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    await this.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: navigationBusinessPage });
    await navigationBusinessPage.storesViewButton.click();

    const storesViewPage = new StoresViewPage(this.page);
    await storesViewPage.siteDropDownFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load stores view page, site drop down filter is not visible',
    });

    if (viewRandomStoreDetails) {
      await this.viewStoreDetails({ storesViewPage });
    }

    return storesViewPage;
  }

  @TestStep('View First Store Details')
  private async viewStoreDetails({
    storesViewPage = new StoresViewPage(this.page),
  }: { storesViewPage?: StoresViewPage } = {}) {
    const firstStoreButton = await storesViewPage.storeCards.firstResult({ timeout: 3000 });
    await firstStoreButton.click();

    const storeDetailsPage = new StoreDetailsPage(this.page);
    await storeDetailsPage.infoButton.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load store details page, info button is not visible',
    });

    storeDetailsPage.fulfillmentAppButton.click();
    await storeDetailsPage.passwordFulfillmentAppTab.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `Failed to load store's fulfillment app details tab, password field is not visible`,
    });

    // Sometimes the button didn't fully load yet, so we need to reclick it
    storeDetailsPage.dropPointsButton.click();
    await storeDetailsPage.storeQRCodeDropPointTab.waitUntilResultsCount({
      expectedCount: 1,
      exactMatch: false,
      timeout: 3000,
    });

    storeDetailsPage.dropPointsButton.click();
    const firstQRCode = await storeDetailsPage.storeQRCodeDropPointTab.firstResult();
    await firstQRCode.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `Failed to load store's drop points tab, store QR code is not visible`,
    });
  }

  @TestStep('Go to vendors view page')
  private async goToVendorsViewPage({
    shouldNavigateToPage = false,
    viewVendorDetails = false,
  }: { shouldNavigateToPage?: boolean; viewVendorDetails?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    await this.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: navigationBusinessPage });
    await navigationBusinessPage.vendorsViewButton.click();

    const vendorsViewPage = new VendorsViewPage(this.page);
    await vendorsViewPage.searchBar.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendors view page, vendor search bar is not visible',
    });

    if (viewVendorDetails) {
      await this.openVendorDetailsPage({ vendorsViewPage });
    }
  }

  @TestStep(`Open First Vendor Details Page`)
  private async openVendorDetailsPage({
    vendorsViewPage = new VendorsViewPage(this.page),
  }: { vendorsViewPage?: VendorsViewPage } = {}) {
    //TODO: change to:
    // await vendorsViewPage.vendorsCards.firstResult();
    // Once backend fixes the config
    const firstVendorResult = await vendorsViewPage.vendorsCards.getByIndex({
      index: 1,
      exactMatch: false,
      timeout: 5000,
    });
    await firstVendorResult.click();

    const vendorDetailsPage = new VendorDetailsPage(this.page);
    await vendorDetailsPage.vendorDetailsContent.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendor details page, vendor content is not visible',
    });

    const firstConfigCard = await vendorDetailsPage.configCards.firstResult();
    await firstConfigCard.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendor details page, config cards are not visible',
    });
  }

  @TestStep(`Go to operations hub for the selected Site`)
  private async goToOperationsHubForTheSite({
    site,
    afterLogin = false,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    afterLogin: boolean;
    shouldNavigateToPage?: boolean;
  }) {
    const navigationBusinessPage = new NavigationBusinessPage(this._page);

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldWaitForLoad: afterLogin,
    });

    if (afterLogin) {
      await navigationBusinessPage.salesSummaryCard.waitToBeVisible();
    }

    await navigationBusinessPage.switchSiteContextButton.click();

    // After login the page might refresh unexpectedly, so if the element isn't visible after click it means we need to do it again
    // if we fail to make the element appear after 5 seconds and additional click, it means there is a bug
    if (await navigationBusinessPage.siteSelectionDropdownList.waitToBeVisible({ timeout: 5000 })) {
      await navigationBusinessPage.siteSelectionDropdownList.selectOption({ optionToSelect: site.name });
    } else {
      await navigationBusinessPage.switchSiteContextButton.click();
      await navigationBusinessPage.siteSelectionDropdownList.selectOption({ optionToSelect: site.name });
    }

    const operationsHubPage = new OperationHubPage(this.page, site);
    await operationsHubPage.searchPackageInput.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load operations hub page, search input is not visible',
    });
  }

  @TestStep('Assert delivery status')
  private async assertDeliveryStatus({
    packagee,
    site,
    shouldNavigateToPage = true,
    assertion,
  }: {
    packagee: Package;
    site: Site;
    shouldNavigateToPage?: boolean;
    assertion: (status: string) => void;
  }) {
    const deliveryCurrentStatus = await this.deliveryDetailsActions.whatIsTheDeliveryStatus({
      site,
      packagee,
      shouldNavigateToPage,
    });

    if (!deliveryCurrentStatus) {
      throw new Error(`Failed to get delivery status`);
    }

    assertion(deliveryCurrentStatus);
  }

  @TestStep(`Open Intercom Modal`)
  private async openIntercomModal({ shouldLogin = true }: { shouldLogin?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);

    if (shouldLogin) {
      await this.perform.login();
      await navigationBusinessPage.intercomButton.locator.waitFor();
    }

    await navigationBusinessPage.intercomButton.click();
    const intercomPage = new IntercomModal(this.page);

    await intercomPage.init();
    await intercomPage.sendUsMessage.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load intercom modal, send us message button is not visible',
    });
  }
}
