/* eslint-disable @typescript-eslint/no-explicit-any */
import { Page } from '@playwright/test';
import DataManager from '../dataManager';
import ViewportTypeEnum from '../enums/viewPortTypeEnum';
import { ActorContext } from '../innerInfra/interfaces/actorContext';
import BasePage from '../innerInfra/pages/basePage';

// Method args infers the original method arguments
// and allows to bind them for type safety in test writing
type MethodArgs<T> = T extends (...args: infer A) => any ? A : never;

export default abstract class BaseActor implements ActorContext {
  protected _page: Page;
  readonly viewPortType: ViewportTypeEnum;

  constructor(page: Page) {
    this._page = page;
    this.viewPortType = DataManager.DataConverter.convertViewPortToViewPortType(this.page.viewportSize());
  }

  // Utility to bind methods automatically with context to original class
  protected bindAction<T extends (...args: Parameters<T>) => ReturnType<T>>(
    action: T,
    context: object,
  ): (...args: MethodArgs<T>) => ReturnType<T> {
    return (...args: MethodArgs<T>) => action.apply(context, args);
  }

  get perform() {
    return {
      refreshPage: this.bindAction(this.refreshPage, this),
      bringPageToFront: this.bindAction(this.bringPageToFront, this),
      navigateToPageIfNeeded: this.bindAction(this.navigateToPageIfNeeded, this),
      waitForPageToLoad: this.bindAction(this.waitForPageToLoad, this),
    };
  }

  get ask() {
    return {
      whatIsPageLanguage: this.bindAction(this.getLanguageLocalOfPage, this),
    };
  }

  abstract get assert();

  abstract get goTo();

  get get() {
    return {
      viewPortType: this.viewPortType,
    };
  }

  get page(): Page {
    return this._page;
  }

  set page(page: Page) {
    this._page = page;
  }

  private async getLanguageLocalOfPage() {
    return this._page.evaluate(() => {
      return navigator.language;
    });
  }

  private async navigateToPageIfNeeded({
    shouldNavigateToPage,
    page,
    shouldWaitForLoad = true,
    shouldUseCustomNavigation = false,
    customNavigation = undefined,
  }: {
    shouldNavigateToPage: boolean;
    page: BasePage;
    shouldWaitForLoad?: boolean;
    shouldUseCustomNavigation?: boolean;
    customNavigation?: () => Promise<void>;
  }) {
    if (shouldNavigateToPage) {
      if (shouldUseCustomNavigation) {
        if (!customNavigation) {
          throw new Error('Custom navigation function is not defined');
        }

        await customNavigation();
      } else {
        await page.navigate(page.getInitialUrl());
      }
      await this.waitForPageToLoad(page, shouldWaitForLoad);
    }
  }

  private async waitForPageToLoad(page: BasePage, shouldWaitForLoad: boolean) {
    await page.waitForLoad({ shouldWaitForEntirePage: shouldWaitForLoad });
  }

  private async refreshPage() {
    await this._page.reload();
  }

  private async bringPageToFront() {
    await this._page.bringToFront();
  }
}
