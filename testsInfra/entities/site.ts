import CurrencyCodesEnum from '../enums/currencyCodesEnum';
import SiteTypesEnum from '../enums/siteTypesEnum';
import { Store } from '../innerInfra/interfaces/store';

export default interface Site {
  type: SiteTypesEnum;
  name: string;
  packageCodePrefix: string;
  currencyCode: CurrencyCodesEnum;
  featureFlags: string[];
  isWithHub: boolean;
  supportedAddressTypes: string[];
  stores: Store[];
}
