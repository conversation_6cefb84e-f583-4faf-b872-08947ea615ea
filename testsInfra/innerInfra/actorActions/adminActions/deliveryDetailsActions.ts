import { expect } from '@playwright/test';
import Package from '../../../entities/package';
import Site from '../../../entities/site';
import { TestStep } from '../../decorators';
import { AdminContext } from '../../interfaces/adminContext';
import { Auth0LoginUser } from '../../interfaces/auth0Login';
import { Logger } from '../../logger';
import OperationHubPage from '../../pages/businessPages/hfs/operationsHub';
import BaseAction from '../baseAction';

export default class DeliveryDetailsActions extends BaseAction<AdminContext & Auth0LoginUser> {
  constructor(actor: AdminContext & Auth0LoginUser) {
    super(actor);
  }

  @TestStep(`Marks delivery as delivered`)
  public async markDeliveryAsDelivered({
    site,
    packagee,
    shouldGoToOperationHubAfterAction = true,
  }: {
    site: Site;
    packagee: Package;
    shouldGoToOperationHubAfterAction?: boolean;
  }) {
    const detailsPage = await this.goToPackageDetails({
      site,
      packagee,
      shouldSwitchToNewPage: !shouldGoToOperationHubAfterAction,
    });

    await detailsPage.threeDotButton.selectOption({ optionToSelect: 'Mark as delivered' });
    await detailsPage.markAsDeliveredButton.click();

    await detailsPage.deliverySuccessfullyUpdatedToast.waitToBeVisible();
    await expect(detailsPage.deliverySuccessfullyUpdatedToast.locator).toBeHidden({ timeout: 10000 });

    if (shouldGoToOperationHubAfterAction) {
      await this.goToOperationHubAfterOpeningDeliveryPage();
    }
  }

  @TestStep(`Seal package`)
  public async sealPackage({
    site,
    packagee,
    shouldGoToOperationHub: shouldGoToHomePage = true,
  }: {
    site: Site;
    packagee: Package;
    shouldGoToOperationHub?: boolean;
  }) {
    const detailsPage = await this.goToPackageDetails({
      site,
      packagee,
      shouldSwitchToNewPage: !shouldGoToHomePage,
    });
    await detailsPage.threeDotButton.selectOption({ optionToSelect: 'Seal delivery' });
    await detailsPage.sealButton.click();

    if (shouldGoToHomePage) {
      await this.goToOperationHubAfterOpeningDeliveryPage();
    }
  }

  private async goToPackageDetails({
    site,
    packagee,
    shouldSwitchToNewPage = true,
  }: {
    site: Site;
    packagee: Package;
    shouldSwitchToNewPage?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);

    await operationHubPage.searchPackageInput.fill(packagee.packageCode);
    const detailsPopup = operationHubPage.page.waitForEvent('popup');
    await operationHubPage.packageInfoNewTabButton.click();

    const newDetailsPage = new OperationHubPage(await detailsPopup, site);
    await newDetailsPage.packageStatus.click();

    if (shouldSwitchToNewPage) {
      this.actor.page = newDetailsPage.page;
    }

    Logger.info(`Delivery details url: ${newDetailsPage.getCurrentUrl()}`, newDetailsPage.getCurrentUrl());
    return newDetailsPage;
  }

  @TestStep(`Admin checks delivery status`)
  public async whatIsTheDeliveryStatus({
    site,
    packagee,
    shouldNavigateToPage = true,
  }: {
    site: Site;
    packagee: Package;
    shouldNavigateToPage?: boolean;
  }) {
    if (!shouldNavigateToPage) {
      const newDetailsPage = new OperationHubPage(this.actor.page, site);

      await newDetailsPage.deliverySuccessfullyUpdatedToast.waitToBeVisible();
      await expect(newDetailsPage.deliverySuccessfullyUpdatedToast.locator).toBeHidden({ timeout: 10000 });

      await this.actor.perform.refreshPage();
      return await newDetailsPage.packageStatus.textContent();
    }

    const newDetailsPage = await this.goToPackageDetails({ site, packagee });
    return await newDetailsPage.packageStatus.textContent();
  }

  @TestStep(`Deliver packages`)
  public async deliverPackages({
    site,
    packagee,
    isLoggedIn = true,
    shouldGoToOperationHub = false,
    shouldGoToOperationHubAfterAction = false,
  }: {
    site: Site;
    packagee: Package;
    isLoggedIn?: boolean;
    shouldGoToOperationHub?: boolean;
    shouldGoToOperationHubAfterAction?: boolean;
  }) {
    if (!isLoggedIn) {
      await this.actor.perform.login();
    }

    if (shouldGoToOperationHub) {
      await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: isLoggedIn });
    }

    await this.markDeliveryAsDelivered({
      site,
      packagee,
      shouldGoToOperationHubAfterAction,
    });
  }

  private async goToOperationHubAfterOpeningDeliveryPage() {
    await this.actor.perform.bringPageToFront();
    await this.actor.perform.refreshPage();
  }
}
