import { expect } from '@playwright/test';
import Package from '../../../entities/package';
import Site from '../../../entities/site';
import HandOverButtonTypesEnum from '../../../enums/handOverButtonTypeEnum';
import { TestStep } from '../../decorators';
import { AdminContext } from '../../interfaces/adminContext';
import { Auth0LoginUser } from '../../interfaces/auth0Login';
import OperationHubPage from '../../pages/businessPages/hfs/operationsHub';
import BaseAction from '../baseAction';

export default class OperationHubActions extends BaseAction<AdminContext & Auth0LoginUser> {
  constructor(actor: AdminContext & Auth0LoginUser) {
    super(actor);
  }

  @TestStep(`Receive packages`)
  public async receivePackages({
    site,
    packages,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    for (const packagee of packages) {
      await this.searchPackage(packagee, { operationHubPage });
      await operationHubPage.receivePackagesButton.click();

      await operationHubPage.manualScanPackageCodeInput.waitToBeVisible({
        shouldThrowError: true,
        shouldThrowSoftError: true,
        errorMessage: 'Receive Packages Page failed to load, manual scan package code input is not visible',
        timeout: 5000,
      });

      await operationHubPage.manualScanPackageCodeInput.fill(packagee.packageCode);
      await operationHubPage.barcodePrintButton.click();
      await operationHubPage.okButton.click();
    }
  }

  @TestStep(`Hand over delivery`)
  public async handoverDelivery({
    site,
    packages,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
  }) {
    if (!packages?.length) {
      throw new Error('Cannot handover delivery without packages');
    }

    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    await this.searchPackage(packages[0], { operationHubPage });
    await (await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.InitialHandOver)).click();

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Handover Delivery' });

    await (await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.AfterScan)).click();
    await (await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.FinalHandOver)).click();
  }

  @TestStep(`Search for package`, 1)
  public async searchPackage(packagee: Package, options: { operationHubPage?: OperationHubPage; site?: Site }) {
    if (!packagee) {
      throw new Error('Package is undefined');
    }

    if (!options.operationHubPage) {
      if (!options.site) {
        throw new Error('Site and page cannot be both undefined');
      }

      options.operationHubPage = new OperationHubPage(this.actor.page, options.site);
      await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage: true, page: options.operationHubPage });
    }

    await options.operationHubPage.searchPackageInput.fill(packagee.packageCode);

    return options.operationHubPage;
  }

  @TestStep(`Prepare Delivery for dispatch`)
  public async prepareDeliveryForDispatch({
    site,
    packages,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    await operationHubPage.searchPackageInput.fill(packages[0].packageCode);
    await operationHubPage.prepareForDispatchButton.click();

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Prepare Delivery for Dispatch' });

    await operationHubPage.updateFooterSubmitButton.click();
    await operationHubPage.doneButton.click();
  }

  @TestStep(`Dispatch delivery`)
  public async dispatchDelivery({
    site,
    packages,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    await operationHubPage.searchPackageInput.fill(packages[0].packageCode);
    await operationHubPage.dispatchButton.click();

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Dispatch Delivery' });

    await operationHubPage.updateFooterSubmitButton.click();
    await operationHubPage.finalDispatchButton.click();
  }

  @TestStep(`Scan packages`)
  private async scanPackagesAfterSearch({
    packages,
    operationHubPage,
    pageName,
  }: {
    operationHubPage: OperationHubPage;
    packages: Package[];
    pageName: string;
  }) {
    await operationHubPage.manualScanPackageCodeInput.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `${pageName} Page failed to load, manual scan package code input is not visible`,
      timeout: 5000,
    });

    for (const [i, packagee] of packages.entries()) {
      await operationHubPage.manualScanPackageCodeInput.fill(packagee.packageCode);
      await this.waitForPackagesToAppearAsScanned({ operationHubPage, expectedPackagesCount: i + 1 });
    }
  }

  @TestStep(`Wait For Scanned Package to appear as scanned`)
  private async waitForPackagesToAppearAsScanned({
    operationHubPage,
    expectedPackagesCount,
  }: {
    operationHubPage: OperationHubPage;
    expectedPackagesCount: number;
  }) {
    await expect(operationHubPage.scannedPackagesCheckers.locator).toHaveCount(expectedPackagesCount);
    const checkers = await operationHubPage.scannedPackagesCheckers.locator.all();
    await Promise.all(checkers.map((checker) => expect(checker).toBeVisible()));
  }

  @TestStep(`Receive packages`)
  public async loginAndReceivePackages({ site, packages }: { site: Site; packages: Package[] }) {
    await this.actor.perform.login();
    await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: true });
    await this.receivePackages({ site, packages });
  }

  @TestStep(`Prepare and dispatch delivery`)
  public async prepareAndDispatchDelivery({
    site,
    packages,
    isLoggedIn = true,
  }: {
    site: Site;
    packages: Package[];
    isLoggedIn?: boolean;
  }) {
    if (!isLoggedIn) {
      await this.actor.perform.login();
      await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: true });
    }

    await this.prepareDeliveryForDispatch({ site, packages });
    await this.dispatchDelivery({ site, packages });
  }
}
