import DropTypesEnum from '../../../enums/dropTypesEnum';
import { TestStep } from '../../decorators';
import { ConsumerContext } from '../../interfaces/consumerContext';
import { DroppingUser } from '../../interfaces/droppingUser';
import { Store } from '../../interfaces/store';
import CheckoutConsumerPage from '../../pages/consumerPages/checkoutConsumerPage';
import HomeConsumerPage from '../../pages/consumerPages/homeConsumerPage';
import BaseAction from '../baseAction';

export default class HomeConsumerActions extends BaseAction<ConsumerContext & DroppingUser> {
  constructor(actor: ConsumerContext & DroppingUser) {
    super(actor);
  }

  @TestStep(`Go to home page`)
  public async goToHomePage({
    fromIntercomModal = false,
    shouldNavigateToPage = true,
  }: { fromIntercomModal?: boolean; shouldNavigateToPage?: boolean } = {}) {
    if (fromIntercomModal) {
      await this.actor.perform.refreshPage();
    }

    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: new HomeConsumerPage(this.actor.page),
    });

    await new HomeConsumerPage(this.actor.page).homeSiteGetStartedButton.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 5000,
      errorMessage: 'Failed to load home page, get started button is not visible',
    });
  }

  @TestStep(`Scans store barcode through app`)
  public async scanStoreBarcodeThroughApp({
    store,
    shouldNavigateToPage = false,
  }: {
    store: Store;
    shouldNavigateToPage?: boolean;
  }) {
    const homePage = new HomeConsumerPage(this.actor.page);

    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: homePage });
    await homePage.homeSiteGetStartedButton.click();

    const storeBarcode = store?.barcode;

    if (!storeBarcode) {
      throw new Error('Store not found on that site, did you choose the wrong site?');
    }

    await this.actor.page.goto(storeBarcode);
    await homePage.storeGetStartedButton.click();
    await homePage.homeSiteGetStartedButton.waitToBeInvisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 5000,
      errorMessage: 'Failed to load drop page, home page is still visible',
    });

    return new CheckoutConsumerPage(homePage.page);
  }

  @TestStep(`Finishing dropping`)
  public async finishDropping({ shouldNavigateToPage = false } = {}) {
    const homePage = new HomeConsumerPage(this.actor.page);

    if (shouldNavigateToPage) {
      await homePage.navigate();
    }

    if (this.actor.dropType === DropTypesEnum.Collection) {
      await homePage.homeSiteFinishDroppingButton.click();
      await homePage.finishedDroppingButton.click();
      await homePage.okGotItButton.click();

      // Every time we finish dropping, we need to pay for the drop pass again on new drops
      this.actor.set.consumerToPayForNextDropPass();
    }
  }
}
