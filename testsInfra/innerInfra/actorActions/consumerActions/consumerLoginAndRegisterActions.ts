import DataManager from '../../../dataManager';
import { TestStep } from '../../decorators';
import { ConsumerContext } from '../../interfaces/consumerContext';
import { DroppingUser } from '../../interfaces/droppingUser';
import { Logger } from '../../logger';
import LoginConsumerPage from '../../pages/consumerPages/loginConsumerPage';
import BaseAction from '../baseAction';

export default class ConsumerLoginAndRegisterActions extends BaseAction<ConsumerContext & DroppingUser> {
  constructor(actor: ConsumerContext & DroppingUser) {
    super(actor);
  }

  @TestStep(`Enter Verification code`)
  public async enterVerificationCode({
    verificationCode,
    navigateToPageIfNeeded = false,
  }: {
    verificationCode: string;
    navigateToPageIfNeeded?: boolean;
  }) {
    if (navigateToPageIfNeeded) {
      await this.login({ verificationCode });
    } else {
      const loginPage = new LoginConsumerPage(this.actor.page);
      await loginPage.confirmationCodeInput.fill(verificationCode);
    }

    return new LoginConsumerPage(this.actor.page);
  }

  @TestStep(`Consumer inputs their phone number in login screen`)
  public async inputPhoneNumber({
    phoneNumber = this.actor.get.phoneNumber,
    country = 'Israel',
    needToNavigateToPage = true,
    submit = false,
  } = {}) {
    if (!phoneNumber || !country) {
      throw new Error('Missing required parameters');
    }

    const loginPage = new LoginConsumerPage(this.actor.page);

    if (needToNavigateToPage) {
      await loginPage.navigate();
    }

    await loginPage.phoneNumberField.fillWithCountryCode(phoneNumber, country);

    if (submit) {
      await this.submitLoginInfo(loginPage);
    }

    return loginPage;
  }

  @TestStep(`Submit login information`)
  private async submitLoginInfo(loginPage: LoginConsumerPage) {
    Logger.info('Removing Intercom popup and Captcha to not interfere with the login');

    await loginPage.removeIntercom();
    await loginPage.removeCaptcha();
    await loginPage.nextButton.click();
  }

  @TestStep(`Consumer logs in`)
  public async login({
    phoneNumber = this.actor.get.phoneNumber,
    verificationCode = DataManager.Consts.MASTER_CODE,
    country = 'Israel',
    needToNavigateToPage = true,
  } = {}) {
    if (!phoneNumber || !verificationCode || !country) {
      throw new Error('Missing required parameters');
    }

    Logger.info('Logging in to Consumer app', phoneNumber);
    await this.inputPhoneNumber({ phoneNumber, country, submit: true, needToNavigateToPage });
    const loginPage = await this.enterVerificationCode({ verificationCode, navigateToPageIfNeeded: false });

    await loginPage.confirmationCodeInput.waitToBeInvisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 5000,
      errorMessage: 'Failed to load home page, verification code input is still visible',
    });
    await this.actor.page.waitForLoadState('load');
  }

  @TestStep(`Consumer registers`)
  public async register(email: string, firstName: string = 'Jane', lastName: string = 'Dow') {
    const consumerLoginPage = new LoginConsumerPage(this.actor.page);
    await consumerLoginPage.firstNameInput.fill(firstName);
    await consumerLoginPage.lastNameInput.fill(lastName);
    await consumerLoginPage.emailInput.fill(email);

    await consumerLoginPage.conditionsCheckbox.click();
    await consumerLoginPage.saveButton.click();
  }

  @TestStep(`Consumer gets the login error message`)
  public async whatIsTheLoginErrorMessage() {
    const consumerLoginPage = new LoginConsumerPage(this.actor.page);
    await consumerLoginPage.phoneNumberField.errorLabel.waitToBeVisible();

    return (await consumerLoginPage.phoneNumberField.errorLabel.isVisible())
      ? await consumerLoginPage.phoneNumberField.errorLabel.textContent()
      : '';
  }

  @TestStep(`Consumer gets the verification code error message`)
  public async whatIsTheVerificationCodeErrorMessage() {
    const consumerLoginPage = new LoginConsumerPage(this.actor.page);
    await consumerLoginPage.verificationCodeErrorLabel.waitToBeVisible();

    return (await consumerLoginPage.verificationCodeErrorLabel.isVisible())
      ? await consumerLoginPage.verificationCodeErrorLabel.textContent()
      : '';
  }

  public async goToLoginPage() {
    const loginPage = await new LoginConsumerPage(this.actor.page);
    await loginPage.navigate();

    await loginPage.phoneNumberField.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 5000,
      errorMessage: 'Failed to load login page, phone number field is not visible',
    });

    return loginPage;
  }

  public async goToRegisterPage({
    phoneNumber = this.actor.get.phoneNumber,
    shouldNavigateThroughUI = true,
  }: { phoneNumber?: string; shouldNavigateThroughUI?: boolean } = {}) {
    const loginPage = await this.goToLoginPage();

    if (shouldNavigateThroughUI) {
      await this.login({ phoneNumber, needToNavigateToPage: false });
    }

    await loginPage.firstNameInput.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 5000,
      errorMessage: 'Failed to load register page, first name input is not visible',
    });

    return loginPage;
  }

  public async goToVerificationCodePage() {
    return await this.inputPhoneNumber({ submit: true });
  }
}
