import { expect } from '@playwright/test';
import DataManager from '../../dataManager';
import Package from '../../entities/package';
import DropTypesEnum from '../../enums/dropTypesEnum';
import ViewportTypeEnum from '../../enums/viewPortTypeEnum';
import { TestStep } from '../decorators';
import { BusinessOwnerContext } from '../interfaces/businessOwnerContext';
import { CheckoutPage } from '../interfaces/checkoutPage';
import { ConsumerContext } from '../interfaces/consumerContext';
import { DroppingUser } from '../interfaces/droppingUser';
import { Logger } from '../logger';
import CheckoutConsumerPage from '../pages/consumerPages/checkoutConsumerPage';
import Button from '../pages/elements/button';
import BaseAction from './baseAction';

export default abstract class CheckoutActions<
  T extends (ConsumerContext | BusinessOwnerContext) & DroppingUser,
> extends BaseAction<T> {
  constructor(actor: T) {
    super(actor);
  }

  @TestStep('What is the package input scan error')
  public async whatIsThePackageInputScanError(checkoutPage?: CheckoutPage) {
    if (checkoutPage) {
      await checkoutPage.scanPackagesInputText.waitToBeVisible();

      return checkoutPage.scanPackageInputError.textContent({ shouldClick: false });
    }

    throw new Error('checkout page is undefined');
  }

  @TestStep(`Add package(s) to the drop`)
  public async addPackages({
    additionalPackages,
    checkoutPage = new CheckoutConsumerPage(this.actor.page),
    shouldBarcodeInputErrorAppear = false,
    shouldNavigateToPage = true,
  }: {
    additionalPackages: Package[];
    checkoutPage?: CheckoutPage;
    shouldBarcodeInputErrorAppear?: boolean;
    shouldNavigateToPage?: boolean;
  }) {
    // We are assuming that if the user did not provide any packages
    // They want to drop a package so we generate the data for them
    // There is no reason to be in this page without trying to add a package, valid or otherwise
    if (additionalPackages.length === 0) {
      additionalPackages = DataManager.DataGenerator.generatePackages(this.actor.site, 1);
    }

    if (shouldNavigateToPage) {
      await checkoutPage.packagesCardButton.click();

      if (this.actor.get.viewPortType === ViewportTypeEnum.Mobile) {
        await checkoutPage.pageNewPackageScannerModalCloseButton.click();
      }
    }

    let wasAPackageAddedToTheDrop = false;

    for (const i in additionalPackages) {
      const packagee = additionalPackages[i];

      await checkoutPage.scanPackagesInputText.fill(packagee.packageCode);

      const wasPackageAdded = await checkoutPage.packageDeleteButton(i).waitToBeVisible({
        shouldThrowError: !shouldBarcodeInputErrorAppear,
        errorMessage: 'Failed to add package, package delete button did not appear',
      });

      if (shouldBarcodeInputErrorAppear) {
        expect(wasPackageAdded, 'Asserting if the package addition failed as expected').toBeFalsy();
        Logger.info(
          `Package validation for ${packagee.packageCode} works as expected, package wasn't added to the drop`,
        );

        continue;
      }

      wasAPackageAddedToTheDrop = true;
      Logger.info(`Added package ${packagee.packageCode} to the drop`, packagee.packageCode);
    }

    await checkoutPage.packageScannerManualSaveButton.click();

    if (!wasAPackageAddedToTheDrop) {
      await checkoutPage.packageScannerSaveErrorPopup.waitToBeVisible({
        shouldThrowError: true,
        errorMessage: "Error popup for empty drop didn't appear",
      });

      expect(await checkoutPage.packageScannerSaveErrorPopup.textContent({ shouldClick: false })).toContain(
        `Please connect bags to the delivery before clicking 'Save'`,
      );

      // We click on the popup to remove it and not interrupt other tests
      await checkoutPage.packageScannerSaveErrorPopup.click();
    }

    return wasAPackageAddedToTheDrop ? additionalPackages : [];
  }

  @TestStep(`Add receipts manually`)
  public async addReceiptsManually({ money = '50', checkoutPage }: { money?: string; checkoutPage: CheckoutPage }) {
    await checkoutPage.receiptsCardButton.click();

    if (this.actor.get.viewPortType === ViewportTypeEnum.Mobile) {
      await checkoutPage.receiptsCameraModalCloseButton.click();
    }

    await checkoutPage.receiptsManualMoneyTextInput.fill(money);
    await checkoutPage.deliveryOptionsSaveButton.click();
  }

  public abstract dropPackages(args: unknown);

  @TestStep(`Select Dropit pass`)
  public async selectDropitPass({
    checkoutPage,
    dropitPass: dropitPassButton,
  }: {
    checkoutPage: CheckoutPage;
    dropitPass?: Button;
  }) {
    await checkoutPage.deliveryOptionsMenuOptionButton.click();

    if (dropitPassButton) {
      await dropitPassButton.click();
    } else {
      await checkoutPage.defaultDeliveryPassOption.click();
    }

    await checkoutPage.deliveryOptionsSaveButton.click();
  }

  protected abstract goToAddressPage(checkoutPage: CheckoutPage): Promise<void>;

  @TestStep(`Add address`)
  public async addAddress({
    address,
    additionalContactInfo,
    checkoutPage,
    shouldClickOnSave = true,
    shouldThrowError = true,
  }: {
    address: string;
    additionalContactInfo?: string;
    checkoutPage: CheckoutPage;
    shouldClickOnSave?: boolean;
    shouldThrowError?: boolean;
  }) {
    await this.goToAddressPage(checkoutPage);
    let addressFound: boolean = false;

    if (this.actor.dropType === DropTypesEnum.Delivery) {
      addressFound = await checkoutPage.addressSearchInput.selectOption({
        optionToSelect: address,
        timeoutAfterAction: 2000,
        shouldThrowError,
      });

      if (additionalContactInfo) {
        await checkoutPage.contactInfoNotesTextInput.fill(additionalContactInfo);
      }
    }

    if (shouldClickOnSave) {
      await checkoutPage.addressSaveButton.click({ shouldScrollIntoView: true });
    }

    return addressFound;
  }
}
