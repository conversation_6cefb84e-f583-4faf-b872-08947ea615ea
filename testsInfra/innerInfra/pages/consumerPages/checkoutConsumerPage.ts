import { Page } from '@playwright/test';
import BaseCheckoutPage from '../checkoutPages/baseCheckoutPage';
import Button from '../elements/button';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';

export default class CheckoutConsumerPage extends BaseCheckoutPage {
  readonly checkoutButton: Button;
  readonly gotItButton: Button;
  readonly promoCodeButton: Button;
  readonly promoCodeInput: TextArea;
  readonly promoCodeApplyButton: Button;
  readonly collectionTypeButton: Button;
  readonly checkoutDiscount: VisualCue;
  readonly checkoutTotal: VisualCue;

  readonly collectionButtonOnAddressModal: Button;
  readonly deliveryButtonOnAddressModal: Button;
  readonly deliveryOptionsMenuOptionButton: Button;
  readonly deliveryBookedButton: Button;

  // Relevant only on PC
  readonly deliveryTypeModalButton: Button;
  readonly collectionTypeModalButton: Button;
  readonly deliveryOptionsSaveButton: Button;

  constructor(page: Page) {
    super(page);

    this.deliveryOptionsSaveButton = new Button(page.getByTestId('deliveryOptionsSaveButton'));
    this.checkoutButton = new Button(page.getByTestId('checkoutButton'));
    this.gotItButton = new Button(page.getByTestId('collectionTimeInfoModalCtaButton'));
    this.promoCodeButton = new Button(page.getByTestId('promoCodeCardButton'));
    this.promoCodeInput = new TextArea(page.getByTestId('promoCodeModalInputTextInput'));
    this.promoCodeApplyButton = new Button(page.getByTestId('promoCodeModalPrimaryButton'));
    this.collectionTypeModalButton = new Button(page.getByTestId('deliveryTypeSelectorModalCollectOnSiteButton'));
    this.deliveryTypeModalButton = new Button(page.getByTestId('deliveryTypeSelectorModalCustomerButton'));
    this.collectionButtonOnAddressModal = new Button(page.getByTestId('deliveryTypeSelectorCollectOnSiteButton'));
    this.deliveryButtonOnAddressModal = new Button(page.getByTestId('deliveryTypeSelectorCustomerButton'));
    this.deliveryOptionsMenuOptionButton = new Button(page.getByTestId('deliveryOptionsCardButton'));
    this.deliveryBookedButton = new Button(page.getByTestId('funModalDeliveryBookedHubOkButton'));

    this.checkoutDiscount = new VisualCue(page.getByTestId('checkoutDiscount'));
    this.checkoutTotal = new VisualCue(page.getByTestId('checkoutTotal'));
  }

  public override getInitialUrl() {
    return super.getInitialUrl() + '/new-delivery';
  }

  override async navigate() {
    await super.navigate();
    await this.gotItButton.click();
  }
}
