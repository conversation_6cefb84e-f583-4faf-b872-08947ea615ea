import { Page } from '@playwright/test';
import Button from '../elements/button';
import PhoneN<PERSON>berField from '../elements/phoneNumberField';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';
import BaseConsumerPage from './baseConsumerPage';

export default class LoginConsumerPage extends BaseConsumerPage {
  readonly nextButton: Button;
  readonly confirmationCodeInput: TextArea;
  readonly firstNameInput: TextArea;
  readonly lastNameInput: TextArea;
  readonly emailInput: TextArea;
  readonly conditionsCheckbox: Button;
  readonly saveButton: Button;
  readonly verificationCodeErrorLabel: VisualCue;
  readonly phoneNumberField: PhoneNumberField;

  constructor(page: Page) {
    super(page);

    // Login
    this.phoneNumberField = new PhoneNumberField(
      page,
      page.getByTestId('consumer_login_phoneInput_input'),
      page.getByTestId('consumer_login_phoneInput_countryPicker'),
      page.getByTestId('consumer_login_phoneInput_dropDown-textInput'),
      page.getByTestId('consumer_login_phoneInput_error'),
    );
    this.nextButton = new Button(page.getByTestId('consumer_login_nextButton').locator('div').first());
    this.confirmationCodeInput = new TextArea(page.getByTestId('consumer_login_confirmationCode'));
    this.verificationCodeErrorLabel = new VisualCue(page.getByTestId('confirmationCode_error_message'));

    // Onboarding form
    this.firstNameInput = new TextArea(page.getByTestId('onBoardingFormFirstNameTextInput'));
    this.lastNameInput = new TextArea(page.getByTestId('onBoardingFormLastNameTextInput'));
    this.emailInput = new TextArea(page.getByTestId('onBoardingFormEmailTextInput'));
    this.conditionsCheckbox = new Button(page.getByRole('checkbox'));
    this.saveButton = new Button(page.getByText("I agree to the T&C's and Privacy PolicySave"));
  }

  public override getInitialUrl() {
    return super.getInitialUrl() + '/login';
  }
}
