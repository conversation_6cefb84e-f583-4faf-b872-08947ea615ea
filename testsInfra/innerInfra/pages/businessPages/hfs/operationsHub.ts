import { Page } from '@playwright/test';
import DataManager from '../../../../dataManager';
import Site from '../../../../entities/site';
import HandOverButtonTypesEnum from '../../../../enums/handOverButtonTypeEnum';
import Button from '../../elements/button';
import DropdownList from '../../elements/dropDownlist';
import ScanPackageInput from '../../elements/scanPackageInput';
import TextArea from '../../elements/textArea';
import VisualCue from '../../elements/visualCue';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class OperationHubPage extends NavigationBusinessPage {
  readonly searchPackageInput: TextArea;
  readonly packageSelection: (packageCode: string) => Button;
  readonly receivePackagesButton: Button;
  readonly manualScanPackageCodeInput: ScanPackageInput;
  readonly barcodePrintButton: Button;
  readonly okButton: Button;
  readonly confirmHandOverPopUpTitle: VisualCue;
  readonly packageLocationStatus: VisualCue;

  readonly packageInfoInput: TextArea;
  readonly packageInfoNewTabButton: Button;
  readonly packageStatus: VisualCue;
  readonly updateFooter: VisualCue;
  private readonly _handOverButton: Button;
  private _site: Site;

  readonly prepareForDispatchButton: Button;
  readonly updateFooterSubmitButton: Button;
  readonly doneButton: Button;
  readonly dispatchButton: Button;
  readonly finalDispatchButton: Button;
  readonly threeDotButton: DropdownList;
  readonly scannedPackagesCheckers: VisualCue;

  readonly markAsDeliveredButton: Button;
  readonly sealButton: Button;

  readonly deliverySuccessfullyUpdatedToast: VisualCue;

  constructor(page: Page, site: Site) {
    super(page);

    this._site = site;
    this.url = this.getInitialUrl();

    // Receive Packages elements
    this.searchPackageInput = new TextArea(page.getByTestId('OPH_enterPackageInput'));
    this.packageSelection = (packageCode: string) => new Button(page.getByText(packageCode));
    this.receivePackagesButton = new Button(page.getByRole('button', { name: 'Receive packages' }));
    this.manualScanPackageCodeInput = new ScanPackageInput({ locator: page.getByTestId('ScanPackageInput.input') });
    this.barcodePrintButton = new Button(page.getByTestId('ReceivePackages_printButton'));
    this.okButton = new Button(page.getByRole('button', { name: 'Ok' }));

    // Handover package elements
    this.confirmHandOverPopUpTitle = new VisualCue(page.getByTestId('ConfirmHandoverDialog.CustomBox'));
    this.packageLocationStatus = new VisualCue(page.getByTestId('RawStatusChip__container'));
    this._handOverButton = new Button(page.getByRole('button', { name: 'Handover' }));
    this.updateFooter = new VisualCue(page.getByTestId('UpdateFooter.Snackbar'));

    // Assert package status elements
    this.scannedPackagesCheckers = new VisualCue(
      page.getByTestId('DeliveryPackageTable.StyledRow.StyledTableCell.CustomBox'),
    );
    this.packageInfoNewTabButton = new Button(page.locator('[data-testid*="NewTabLink"][href]'));
    this.packageStatus = new VisualCue(page.getByTestId('ItemDetailsHeader.PageTitleContainer.CustomBox.div'));

    // Newly added elements for dispatching packages
    this.prepareForDispatchButton = new Button(page.getByRole('button', { name: 'Prepare for dispatch' }));
    this.updateFooterSubmitButton = new Button(page.getByTestId('updateFooterSubmitButton'));
    this.doneButton = new Button(page.getByRole('button', { name: 'Done' }));
    this.dispatchButton = new Button(page.getByRole('button', { name: 'Dispatch', exact: true }));
    this.finalDispatchButton = new Button(page.getByRole('button', { name: 'DISPATCH' }));
    this.threeDotButton = new DropdownList(
      page.getByTestId('DeliveryActionSelector.ThreeDotButton'),
      page.getByTestId('StaticSelectorListItem.CustomTypography'),
    );
    this.markAsDeliveredButton = new Button(page.getByRole('button', { name: 'Mark As Delivered' }));
    this.sealButton = new Button(page.getByRole('button', { name: 'Seal' }));

    // Toast notification
    this.deliverySuccessfullyUpdatedToast = new VisualCue(page.getByTestId('CustomSnackbar.StyledContainer.CustomBox'));
  }

  public override getInitialUrl(): string {
    return `${super.getInitialUrl()}site/${DataManager.DataConverter.convertSiteNameToLocatorString(this._site?.name)}/operations-hub`;
  }

  // There are 3 different buttons with the same ID that appear in different places on the handover flow
  async getHandOverButton(handOverButtonTypeEnum: HandOverButtonTypesEnum) {
    switch (handOverButtonTypeEnum) {
      case HandOverButtonTypesEnum.InitialHandOver:
        await this.packageLocationStatus.click();
        break;
      case HandOverButtonTypesEnum.AfterScan:
        await this.updateFooter.click();
        break;
      case HandOverButtonTypesEnum.FinalHandOver:
        await this.confirmHandOverPopUpTitle.click();
        break;
      default:
        throw new Error('Not a supported handover button type');
    }
    return this._handOverButton;
  }
}
