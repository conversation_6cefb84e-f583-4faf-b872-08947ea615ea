import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import { LoginType } from '../../../entities/loginType';
import { Logger } from '../../logger';
import BasePage from '../basePage';
import Button from '../elements/button';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';

export default class Auth0LoginPage extends BasePage {
  readonly emailInput: TextArea;
  readonly passwordInput: TextArea;
  readonly continueButton: Button;
  readonly errorMessage: VisualCue = new VisualCue(this.page.locator('#error-element-password'));
  readonly loginType: LoginType;

  constructor(page: Page, loginType: LoginType = DataManager.Consts.LoginTypes.Business) {
    super(page, loginType);

    this.loginType = loginType;

    this.emailInput = new TextArea(this.page.getByRole('textbox', { name: 'Em<PERSON>', exact: false }));
    this.passwordInput = new TextArea(this.page.getByRole('textbox', { name: 'Password' }));
    this.continueButton = new Button(this.page.getByRole('button', { name: 'Login' }));
  }

  public override getInitialUrl() {
    return this.loginType;
  }

  override async navigate() {
    await this.interceptLogoRetrieval();
    await this.blockPrintPopup();

    return super.navigate();
  }

  private async interceptLogoRetrieval() {
    // Logo Retrieval is dependent on Auth0, which can take a lot of time(almost 20 seconds in the time of writing)
    // To prevent this, we will intercept the request and abort it
    return this.page.route('*/**/**/**/Dropit_Logo_200.png', async (route) => {
      Logger.info('Blocking logo retrieval, Intercepted request:\n', route.request().url());
      await route.abort();
    });
  }

  async blockPrintPopup() {
    const printPopupRoutes = this.getPrintPopupRoutes();

    for (const route of printPopupRoutes) {
      await this.page.route(route, async (route) => {
        Logger.info(
          `Blocking print popup, Intercepted request:\n${route.request().url()}\nIf you wish to view the file, go to the url`,
          route.request().url(),
        );

        await route.abort();
      });
    }
  }

  private getPrintPopupRoutes() {
    const routeSuffix = '/**';
    const routePrefix = '**/cdn/**/';

    return [`${routePrefix}sorting-labels${routeSuffix}`, `${routePrefix}dispatch-labels${routeSuffix}`];
  }

  // the element is not apart of the dom, so we can only get it with evaluate
  async getEmptyFieldValidationState(field: 'email' | 'password') {
    const input = field === 'email' ? this.emailInput : this.passwordInput;

    const validationState = await input.locator.evaluate((element: HTMLInputElement) => {
      return {
        isValid: element.validity.valid,
        isValueMissing: element.validity.valueMissing,
        message: element.validationMessage,
      };
    });

    return validationState;
  }
}
