import { Page } from '@playwright/test';
import Button from '../elements/button';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';
import BaseFulfillmentPage from './baseFulfillmentPage';

export default class HomeFulfillmentPage extends BaseFulfillmentPage {
  readonly businessNameButton: Button;
  readonly storeLoginStoreCodeInput: TextArea;
  readonly newDeliveryButton: Button;
  readonly verificationCodeTitle: VisualCue;
  readonly verificationCodeErrorMessage: VisualCue;

  constructor(page: Page) {
    super(page);

    this.businessNameButton = new Button(page.getByTestId('businessName'));
    this.storeLoginStoreCodeInput = new TextArea(page.getByTestId('storeLoginStoreCode'));
    this.newDeliveryButton = new Button(page.getByTestId('HomeScreen_QuickAction_NewDelivery'));
    this.verificationCodeErrorMessage = new VisualCue(page.getByTestId('storeLoginWrongCodeMessage'));

    // i stands for ignore case, that even if text will change from code to Code it will still work
    this.verificationCodeTitle = new VisualCue(page.locator(':text-matches("Insert code|Wrong code", "i")'));
  }
}
