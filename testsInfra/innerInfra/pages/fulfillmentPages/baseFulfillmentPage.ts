import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import FulfillmentMenuOptionsEnum from '../../../enums/fulfillmentMenuOptionsEnum';
import BasePage from '../basePage';
import Button from '../elements/button';

export default class BaseFulfillmentPage extends BasePage {
  readonly appHeaderMenuButton: Button;
  private readonly sideMenuDispatchButton: Button;
  private readonly sideMenuTrackOrdersButton: Button;
  readonly intercomButton: Button;

  constructor(page: Page, url?: string) {
    super(page, url);

    this.appHeaderMenuButton = new Button(page.getByTestId('AppHeader_menu-button'));
    this.sideMenuDispatchButton = new Button(page.getByTestId('SideMenuButton_Dispatch'));
    this.sideMenuTrackOrdersButton = new Button(page.getByText('0Track Orders'));
    this.intercomButton = new Button(page.getByTestId('AppHeader_intercom-button'));
  }

  protected override getUrlPattern(): RegExp {
    return /fulfillment-app/;
  }

  public override getInitialUrl() {
    return `https://fulfillment-app.${DataManager.Consts.TESTING_ENV}.drpt.io`;
  }

  public async selectMenuOption(fulfillmentMenuOption: FulfillmentMenuOptionsEnum) {
    await this.appHeaderMenuButton.click();

    switch (fulfillmentMenuOption) {
      case FulfillmentMenuOptionsEnum.Dispatch:
        await this.sideMenuDispatchButton.click();

        break;

      case FulfillmentMenuOptionsEnum.TrackOrders:
        await this.sideMenuTrackOrdersButton.click();
        break;

      default:
        throw new Error(`Unsupported menu option: ${fulfillmentMenuOption}`);
    }
  }
}
