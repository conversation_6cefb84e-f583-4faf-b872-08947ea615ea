/* eslint-disable @typescript-eslint/no-require-imports */
import { z } from 'zod';
import Site from '../../entities/site';
import { Logger } from '../logger';

import DefaultSiteConfigs from '../../../envFiles/sitesConfig/default';

type BaseSitesCollection = typeof DefaultSiteConfigs;

// Allow for additional sites in specific environments
// These will not appear in in the autocomplete in the ide
// But will be usable and shown in the playwright test extension(if they are related to naming)
type SitesCollection = BaseSitesCollection & Record<string, Site>;

export default class Consts {
  private readonly envSchema = z.object({
    USER_NAME: z.string().min(1, 'USER_NAME is required'),
    PASSWORD: z.string().min(1, 'PASSWORD is required'),
    PHONE_NUMBER1: z.string().min(9, 'PHONE_NUMBER1 is required'),
    PHONE_NUMBER2: z.string().min(9, 'PHONE_NUMBER2 is required'),
    PHONE_NUMBER3: z.string().min(9, 'PHONE_NUMBER3 is required'),
    CONSUMER_NUMBER_WITH_DROPS: z.string().min(9, 'CONSUMER_NUMBER_WITH_DROPS is required'),
    CONSUMER_NUMBER_WITH_DROPS_COUNTRY: z.string().min(1, 'CONSUMER_NUMBER_WITH_DROPS_COUNTRY is required'),
    TESTING_ENV: z.string().min(2, 'TESTING_ENV is required'),
    MASTER_CODE: z.string().min(4, 'MASTER_CODE is required'),
    CREDIT_CARD_NUMBER: z.string().min(16, 'CREDIT_CARD_NUMBER is required'),
    CREDIT_CARD_EXPIRY_DATE: z.string().min(5, 'CREDIT_CARD_EXPIRY_DATE is required'),
    CREDIT_CARD_CVV: z.string().min(3, 'CREDIT_CARD_CVV is required'),
    CREDIT_CARD_HOLDER_NAME: z.string().min(1, 'CREDIT_CARD_HOLDER_NAME is required'),
    ZEPHYR_AUTH_TOKEN: z.string().min(1, 'ZEPHYR AUTH TOKEN is needed'),
    AUTOMATION_CONSUMER_DEFAULT_EMAIL: z.string().min(1, 'AUTOMATION_CONSUMER_DEFAULT_EMAIL is needed'),
  });

  private readonly parsedEnv = this.envSchema.safeParse(process.env);

  BUSINESS_ADMIN_EMAIL!: string;
  BUSINESS_ADMIN_PASSWORD!: string;
  PHONE_NUMBER1!: string;
  PHONE_NUMBER2!: string;
  PHONE_NUMBER3!: string;
  CONSUMER_NUMBER_WITH_DROPS!: string;
  CONSUMER_NUMBER_WITH_DROPS_COUNTRY!: string;
  TESTING_ENV!: string;
  RUNNING_ENV!: string;
  MASTER_CODE!: string;
  CREDIT_CARD_NUMBER!: string;
  CREDIT_CARD_EXPIRY_DATE!: string;
  CREDIT_CARD_CVV!: string;
  CREDIT_CARD_HOLDER_NAME!: string;
  DEBUG_ENV = 'local';
  DEV_ENV = 'dev';
  QA_ENV = 'qa';
  STAGING_ENV = 'staging';
  LONG_ACTION_TIMEOUT = 10000;
  ERROR_COLOR = 'rgb(252, 106, 132)';
  ZEPHYR_AUTH_TOKEN!: string;
  AUTOMATION_CONSUMER_DEFAULT_EMAIL!: string;

  LoginTypes = {
    Business: '',
    Fulfillment: '',
  };

  Sites: SitesCollection;

  readonly Tags = {
    BUSINESS_APP_TAG: '@BusinessApp',
    CONSUMER_APP_TAG: '@ConsumerApp',
    FULFILLMENT_APP_TAG: '@FulfillmentApp',
    STRIPE_TAG: '@Stripe',
    SANITY_TAG: '@Sanity',
    CONSUMER_DROP_TAG: '@ConsumerDrop',
    QA_TAG: '@QA',
    DEV_TAG: '@Dev',
    NEGATIVE_TAG: '@Negative',
    STAGING_TAG: '@Staging',
    SMOKE_TAG: '@Smoke',
  };

  throwErrorIfAnyRequiredEnvVarIsNotSet() {
    const ciENV = process.env.ENV;
    this.RUNNING_ENV = ciENV ? 'Semaphore' : this.DEBUG_ENV;

    Logger.info(`Running on: ${this.RUNNING_ENV}`);

    if (!this.parsedEnv.success) {
      throw new Error(
        `❌ Missing or invalid environment variables:\n${Object.keys(this.parsedEnv.error.format())
          .filter((key) => key !== '_errors')
          .join('\n')}\n  make sure everything is set in the relevant env file`,
      );
    }

    const env = this.parsedEnv.data;
    this.TESTING_ENV = ciENV ?? env.TESTING_ENV;
    Logger.info(`Testing on: ${this.TESTING_ENV}`);

    this.ZEPHYR_AUTH_TOKEN = env.ZEPHYR_AUTH_TOKEN;
    this.BUSINESS_ADMIN_EMAIL = env.USER_NAME;
    this.BUSINESS_ADMIN_PASSWORD = env.PASSWORD;
    this.MASTER_CODE = env.MASTER_CODE;
    this.PHONE_NUMBER1 = env.PHONE_NUMBER1;
    this.PHONE_NUMBER2 = env.PHONE_NUMBER2;
    this.PHONE_NUMBER3 = env.PHONE_NUMBER3;
    this.CONSUMER_NUMBER_WITH_DROPS = env.CONSUMER_NUMBER_WITH_DROPS;
    this.CONSUMER_NUMBER_WITH_DROPS_COUNTRY = env.CONSUMER_NUMBER_WITH_DROPS_COUNTRY;
    this.CREDIT_CARD_NUMBER = env.CREDIT_CARD_NUMBER;
    this.CREDIT_CARD_EXPIRY_DATE = env.CREDIT_CARD_EXPIRY_DATE;
    this.CREDIT_CARD_CVV = env.CREDIT_CARD_CVV;
    this.CREDIT_CARD_HOLDER_NAME = env.CREDIT_CARD_HOLDER_NAME;
    this.AUTOMATION_CONSUMER_DEFAULT_EMAIL = env.AUTOMATION_CONSUMER_DEFAULT_EMAIL;
    this.Sites = this.getSiteConfigForEnvironment();
    this.LoginTypes = {
      Business: `https://stores.${this.TESTING_ENV}.drpt.io/business/`,
      Fulfillment: `https://fulfillment-app.${this.TESTING_ENV}.drpt.io/home`,
    } as const;
  }

  private getSiteConfigForEnvironment(): SitesCollection {
    try {
      return require(`../../../envFiles/sitesConfig/${this.TESTING_ENV}.ts`).default;
    } catch (error) {
      Logger.info(`Config for ${this.TESTING_ENV} not found, using default config`);
      Logger.debug(error);

      try {
        return require('../../../envFiles/sitesConfig/default.ts').default;
      } catch (error) {
        Logger.error(`Default config not found, please check the path and make sure the file exists`);

        throw error;
      }
    }
  }
}
