import Button from '../pages/elements/button';
import DropdownListWithSearch from '../pages/elements/dropDownlistWithSearch';
import ScanPackageInput from '../pages/elements/scanPackageInput';
import TextArea from '../pages/elements/textArea';
import VisualCue from '../pages/elements/visualCue';

export interface CheckoutPage {
  // Package related elements
  packagesCardButton: Button;
  scanPackagesInputText: ScanPackageInput;
  packageScannerManualSaveButton: Button;
  packageDeleteButton: (packageNumber: string) => Button;
  pageNewPackageScannerModalCloseButton: Button;
  scanPackageInputError: VisualCue;
  packageScannerSaveErrorPopup: VisualCue;

  // Receipt related elements
  receiptsCardButton: Button;
  receiptsManualMoneyTextInput: TextArea;
  receiptsCameraModalCloseButton: Button;

  // Address related elements
  addressMenuOptionButton: Button;
  addressSearchInput: DropdownListWithSearch;
  addressSaveButton: Button;
  contactInfoNotesTextInput: TextArea;

  // Delivery options related elements
  deliveryOptionsMenuOptionButton: Button;
  deliveryOptionsSaveButton: Button;
  defaultDeliveryPassOption: Button;
}
