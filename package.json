{"name": "arya_stark", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:qa": "playwright test --grep @QA", "test:dev": "playwright test --grep @DEV", "test:staging": "playwright test --grep @STAGING", "env:encrypt": "../scripts/sops-kms.sh -a encrypt", "env:decrypt": "../scripts/sops-kms.sh -a decrypt", "extract-zip": "if [ ! -d ~/Downloads/${REPORT_NAME:-playwright-report} ]; then unzip -o ~/Downloads/${REPORT_NAME:-playwright-report}.zip -d ~/Downloads; else echo 'Folder already exists, skipping extraction.'; fi", "ensure-port": "lsof -t -i :9323 && kill -9 $(lsof -t -i :9323) || echo 'Port 9323 is free'", "report:local": "npx playwright show-report", "report:pipeline": "npm run extract-zip && npm run ensure-port && npx playwright show-report ~/Downloads/${REPORT_NAME:-playwright-report}", "report:pipeline:delete": "rimraf ~/Downloads/${REPORT_NAME:-playwright-report} && rimraf ~/Downloads/${REPORT_NAME:-playwright-report}.zip", "clear-cache": "echo '🧹 Clearing all caches...' && npm run clear-cache:node && npm run clear-cache:playwright && npm run clear-cache:eslint && npm run clear-cache:reports && echo '✅ All caches cleared! IDE simulation complete.'", "clear-cache:node": "echo '📦 Clearing Node.js cache...' && rm -rf node_modules/.cache && npm cache clean --force", "clear-cache:playwright": "echo '🎭 Clearing Playwright cache...' && rimraf test-results && rimraf playwright-report && npx playwright install", "clear-cache:eslint": "echo '🔍 Clearing ESLint cache...' && rimraf .eslintcache", "clear-cache:reports": "echo '📊 Clearing report artifacts...' && rimraf results.xml"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/js": "^9.22.0", "@playwright/test": "^1.50.1", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-formatter-junit": "^8.40.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "playwright-zephyr": "^1.2.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0"}, "dependencies": {"@faker-js/faker": "^9.8.0", "axios": "^1.8.3", "dotenv": "^16.4.7", "uuid": "^11.1.0", "zod": "^3.24.2"}}