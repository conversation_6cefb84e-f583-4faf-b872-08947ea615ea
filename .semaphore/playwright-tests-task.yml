version: v1.0
name: Playwright Test
agent:
  machine:
    type: e1-standard-2
    os_image: ubuntu2004
execution_time_limit:
  hours: 2
global_job_config:
  prologue:
    commands:
      - gcloud auth activate-service-account --key-file=.secrets/semaphore2.json
      - gcloud auth configure-docker -q
      - echo "Download scripts from $SCRIPTS_BUCKET"
      - 'gsutil cp -r gs://$SCRIPTS_BUCKET/v2/installSecretV3.sh /home/<USER>/'
      - 'gsutil cp -r gs://$SCRIPTS_BUCKET/slack_notifications_qa_automation.sh /home/<USER>/'
      - sem-version node 22
      - sudo mkdir -p /etc/apt/keyrings
      - 'curl -fsSL https://packages.openvpn.net/packages-repo.gpg | sudo tee /etc/apt/keyrings/openvpn.asc'
      - 'echo "deb [signed-by=/etc/apt/keyrings/openvpn.asc] https://packages.openvpn.net/openvpn3/debian focal main" | sudo tee /etc/apt/sources.list.d/openvpn-packages.list'
      - sudo apt update && sudo apt install -y openvpn3
blocks:
  - name: Playwright Tests
    dependencies: []
    task:
      jobs:
        - name: Playwright Test
          commands:
            - export ENV="${ENV:=qa}"
            - echo "ENV is $ENV"
            - checkout
            - chmod +x ../installSecretV3.sh
            - ../installSecretV3.sh
            - mkdir playwright-reports
            - cache restore secret-$SEMAPHORE_PROJECT_NAME-$(checksum $HOME/$SEMAPHORE_PROJECT_NAME/envFiles/common.secret.env)
            - cache restore secret-$SEMAPHORE_PROJECT_NAME-$(checksum $HOME/$SEMAPHORE_PROJECT_NAME/envFiles/${ENV}.secret.env)
            - npm ci
            - echo "Running Playwright Tests"
            - npx playwright install --with-deps
            - sudo openvpn3 config-import --config ../client.ovpn
            - sudo openvpn3 session-start --config ../client.ovpn
            - npm run test:${ENV}
      secrets:
        - name: OPENVPN
        - name: SOPS
        - name: GCP

      epilogue:
        always:
          commands:
            - zip -r playwright-report.zip playwright-report
            - mv playwright-report.zip playwright-reports/
            - '[[ -f results.xml ]] && test-results publish results.xml'
            - artifact push workflow playwright-reports
            - chmod +x ../slack_notifications_qa_automation.sh
            - ../slack_notifications_qa_automation.sh
after_pipeline:
  task:
    jobs:
      - name: Publish Results
        commands:
          - test-results gen-pipeline-report
