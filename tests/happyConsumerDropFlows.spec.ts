import { expect } from '@playwright/test';
import AdminUser from '../testsInfra/actors/admin';
import { ConsumerUser } from '../testsInfra/actors/consumer';
import DataManager from '../testsInfra/dataManager';
import CreditCard from '../testsInfra/entities/creditCard';
import DeliveryStatusEnum from '../testsInfra/enums/deliveryStatusEnum';
import DropTypesEnum from '../testsInfra/enums/dropTypesEnum';
import PromoCouponsCodesEnum from '../testsInfra/enums/promoCouponsCodesEnum';
import { test } from './baseTest';

const numsOfPackagesToGenerate: number[] = [1, 3];

const xPackagesInASingleDeliveryText = (numOfPackagesInDelivery: number) =>
  `${numOfPackagesInDelivery} packages in a single delivery at`;

const expectTitle = 'Asserting the package status';

test.describe('Happy Multiple Store Flows:', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Two drops in single delivery Collection only site',
      ticketNumber: 64,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.CONSUMER_DROP_TAG,
      ],
    },
    async ({ smallPcPage, mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const admin = new AdminUser(smallPcPage);
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        dropType: DropTypesEnum.Collection,
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        shouldFinishDropping: false,
        store: consumer.site.stores[0],
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        shouldLogin: false,
        store: consumer.site.stores[1],
      });

      await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );
});

test.describe('Happy Single Store Flows:', () => {
  for (const numOfPackagesToGenerate of numsOfPackagesToGenerate) {
    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `${xPackagesInASingleDeliveryText(numOfPackagesToGenerate)} Collection only site`,
        ticketNumber: numOfPackagesToGenerate === 1 ? 1 : 53,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          DataManager.Consts.Tags.CONSUMER_APP_TAG,
          DataManager.Consts.Tags.BUSINESS_APP_TAG,
          DataManager.Consts.Tags.CONSUMER_DROP_TAG,
        ],
      },
      async ({ smallPcPage, mobilePage }) => {
        const site = DataManager.Consts.Sites.CollectionOnlySite;
        const admin = new AdminUser(smallPcPage);
        const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
          dropType: DropTypesEnum.Collection,
        });

        await consumer.perform.existingConsumerDroppingAPackage({
          packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
        });
        await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
        await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

        await admin.assert.deliveryStatus({
          packagee: consumer.droppedPackages[0],
          site: consumer.site,
          assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
        });
      },
    );

    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `Collection with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate)} Collection and Delivery site`,
        ticketNumber: numOfPackagesToGenerate === 1 ? 2 : 54,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          DataManager.Consts.Tags.CONSUMER_APP_TAG,
          DataManager.Consts.Tags.BUSINESS_APP_TAG,
          DataManager.Consts.Tags.CONSUMER_DROP_TAG,
        ],
      },
      async ({ smallPcPage, mobilePage }) => {
        const site = DataManager.Consts.Sites.CollectionAndDeliverySite;
        const admin = new AdminUser(smallPcPage);
        const consumer = new ConsumerUser(mobilePage, 'Itzik Beja', DataManager.Consts.PHONE_NUMBER2, site, {
          dropType: DropTypesEnum.Collection,
        });

        await consumer.perform.existingConsumerDroppingAPackage({
          packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
        });
        await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
        await admin.perform.prepareAndDispatchDelivery({ site: consumer.site, packages: consumer.droppedPackages });
        await admin.perform.deliverPackages({
          site: consumer.site,
          packagee: consumer.droppedPackages[0],
          shouldGoToOperationHubAfterAction: false,
        });

        await admin.assert.deliveryStatus({
          packagee: consumer.droppedPackages[0],
          site: consumer.site,
          assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
          shouldNavigateToPage: false,
        });
      },
    );

    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `Delivery with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate)} Collection and Delivery site`,
        ticketNumber: numOfPackagesToGenerate === 1 ? 3 : 55,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          DataManager.Consts.Tags.CONSUMER_APP_TAG,
          DataManager.Consts.Tags.BUSINESS_APP_TAG,
          DataManager.Consts.Tags.CONSUMER_DROP_TAG,
        ],
      },
      async ({ smallPcPage, mobilePage }) => {
        const site = DataManager.Consts.Sites.CollectionAndDeliverySite;
        const admin = new AdminUser(smallPcPage);
        const consumer = new ConsumerUser(mobilePage, 'Sadfa Asdfa', DataManager.Consts.PHONE_NUMBER3, site, {
          dropType: DropTypesEnum.Delivery,
        });

        await consumer.perform.existingConsumerDroppingAPackage({
          packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
        });
        await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
        await admin.perform.sealPackage({
          site: consumer.site,
          packagee: consumer.droppedPackages[0],
          shouldGoToOperationHub: true,
        });

        await admin.perform.prepareAndDispatchDelivery({ site: consumer.site, packages: consumer.droppedPackages });
        await admin.perform.deliverPackages({
          site: consumer.site,
          packagee: consumer.droppedPackages[0],
          shouldGoToOperationHubAfterAction: false,
        });

        await admin.assert.deliveryStatus({
          packagee: consumer.droppedPackages[0],
          site: consumer.site,
          shouldNavigateToPage: false,
          assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
        });
      },
    );
  }

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Stripe Full Payment on delivery with ${numsOfPackagesToGenerate[0]} package Collection only site`,
      ticketNumber: 47,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.STRIPE_TAG,
        DataManager.Consts.Tags.CONSUMER_DROP_TAG,
      ],
    },
    async ({ smallPcPage, mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const admin = new AdminUser(smallPcPage);
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        creditCard: new CreditCard(),
      });

      await consumer.perform.existingConsumerDroppingAPackage({ promoCode: PromoCouponsCodesEnum.FullPrice });
      await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Stripe Partial Payment With Promo Code on delivery with ${numsOfPackagesToGenerate[0]} package Collection only site`,
      ticketNumber: 48,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.STRIPE_TAG,
        DataManager.Consts.Tags.CONSUMER_DROP_TAG,
      ],
    },
    async ({ smallPcPage, mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const admin = new AdminUser(smallPcPage);
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        creditCard: new CreditCard(),
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        promoCode: PromoCouponsCodesEnum.HalfPrice,
        expectedAmountToPay: 5,
      });
      await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) => expect(status, { message: expectTitle }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Stripe Failed Payment with ${numsOfPackagesToGenerate[0]} package Collection only site`,
      ticketNumber: 80,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.STRIPE_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        creditCard: new CreditCard(),
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        promoCode: PromoCouponsCodesEnum.FullPrice,
        packages: DataManager.DataGenerator.generatePackages(site, numsOfPackagesToGenerate[0]),
        failDrop: true,
      });

      await test.step('Assert Payment Error', async () => {
        const paymentErrorText = await consumer.ask.whatIsThePaymentErrorText();

        expect(paymentErrorText).toContain('To finalize the drop, please complete your payment.');
      });
    },
  );
});
