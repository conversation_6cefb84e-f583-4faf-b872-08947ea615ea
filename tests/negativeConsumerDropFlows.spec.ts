import { expect } from '@playwright/test';
import AdminUser from '../testsInfra/actors/admin';
import { ConsumerUser } from '../testsInfra/actors/consumer';
import DataManager from '../testsInfra/dataManager';
import Package from '../testsInfra/entities/package';
import DeliveryStatusEnum from '../testsInfra/enums/deliveryStatusEnum';
import DropTypesEnum from '../testsInfra/enums/dropTypesEnum';
import { test } from './baseTest';

const testsData = [
  {
    country: 'United Kingdom',
    address: 'Derech Menachem Begin 22', // The British consider <PERSON><PERSON><PERSON> as a terrorist, they would never has an address with his name
    site: DataManager.Consts.Sites.CollectionAndDeliverySite,
    phoneNumber: DataManager.Consts.PHONE_NUMBER2,
  },
];

test.describe('Negative Consumer Drop Flows', () => {
  for (const currTestData of testsData) {
    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `Drop with an invalid Address in ${currTestData.country}`,
        ticketNumber: 46,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.NEGATIVE_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          DataManager.Consts.Tags.CONSUMER_APP_TAG,
        ],
      },
      async ({ mobilePage }) => {
        const site = currTestData.site;
        const consumer = new ConsumerUser(mobilePage, 'Automation User', currTestData.phoneNumber, site, {
          dropType: DropTypesEnum.Delivery,
          address: currTestData.address,
        });

        await consumer.perform.login();
        await consumer.perform.scanStoreBarcodeThroughApp({ store: site.stores[0] });
        expect(
          await consumer.perform.addAddress({
            address: currTestData.address,
            additionalContactInfo: 'Additional Contact Info',
            shouldThrowError: false,
            shouldClickOnSave: false,
          }),
          'Asserting if invalid address was found',
        ).toBeFalsy();
      },
    );
  }

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop with an invalid Package code',
      ticketNumber: 17,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER1,
        DataManager.Consts.Sites.CollectionOnlySite,
      );

      const InvalidPackages = DataManager.DataGenerator.generateInvalidPackageCodes(consumer.site);

      await consumer.perform.login();
      await consumer.perform.scanStoreBarcodeThroughApp({ store: consumer.site.stores[0] });
      await consumer.perform.dropPackages({
        packages: [InvalidPackages[0]],
        shouldNavigateToPage: false,
        shouldPackageAdditionSucceed: false,
      });

      await consumer.assert.invalidPackageCodes(InvalidPackages, (errorMessage, code) =>
        expect
          .soft(errorMessage, {
            message: `Asserting the invalid package code: ${code}\nif the received string is empty no error message appeared`,
          })
          .toContain('Bag ID is not valid'),
      );
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop with a valid and invalid package code should only drop the valid one',
      ticketNumber: 100,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage, smallPcPage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER1,
        DataManager.Consts.Sites.CollectionOnlySite,
      );

      const adminUser = new AdminUser(smallPcPage);
      const validPackage = DataManager.DataGenerator.generatePackages(consumer.site, 1);
      const InvalidPackage = new Package('123456789');

      await consumer.perform.login();
      await consumer.perform.scanStoreBarcodeThroughApp({ store: consumer.site.stores[0] });
      await consumer.perform.dropPackages({
        packages: [InvalidPackage],
        shouldNavigateToPage: false,
        shouldPackageAdditionSucceed: false,
      });

      await consumer.perform.enterPackageCode(validPackage[0], {
        shouldPackageAdditionFail: false,
      });

      await consumer.perform.checkout();
      await consumer.perform.finishDropping();

      await adminUser.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await adminUser.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });
      await adminUser.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) => expect(status, { message: 'Asserting drop status' }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop with an empty Package code',
      ticketNumber: 17,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER1,
        DataManager.Consts.Sites.CollectionOnlySite,
      );

      const InvalidPackage = new Package('');

      await consumer.perform.login();
      await consumer.perform.scanStoreBarcodeThroughApp({ store: consumer.site.stores[0] });

      // The assertions for the failed save are done in the checkout out actions
      // To avoid repeating code with other tests
      await consumer.perform.dropPackages({
        packages: [InvalidPackage],
        shouldNavigateToPage: false,
        shouldPackageAdditionSucceed: false,
      });

      expect(consumer.droppedPackages).toHaveLength(0);
    },
  );
});
