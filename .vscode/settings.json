{"editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.semanticHighlighting.enabled": true, "editor.semanticTokenColorCustomizations": {"rules": {"interface": "#a6d3a0"}}, "eslint.validate": ["javascript", "typescript"], "prettier.prettierPath": "./node_modules/prettier", "typescript.tsdk": "node_modules/typescript/lib", "cSpell.words": ["<PERSON><PERSON>", "Asdfa", "atאא", "bardcodes", "<PERSON><PERSON>", "Bicester", "Bizi", "<PERSON><PERSON><PERSON><PERSON>", "Derech", "DROPIT", "dropitshopping", "drpt", "<PERSON>mmer", "frameattached", "framenavigated", "handovering", "Handsfree", "ITHA", "Itzik", "Menachem", "missinglocal", "NOLOWERCASEORNUMBERS", "noupper<PERSON><PERSON><PERSON><PERSON>", "nums", "<PERSON><PERSON><PERSON>", "packagee", "plainaddress", "Pressable", "Sad<PERSON>", "sameday", "<PERSON><PERSON><PERSON>", "TMTN", "UKHA", "undropped", "uuidv", "אאאא", "<PERSON>ו<PERSON><PERSON><PERSON>ן"], "cSpell.ignorePaths": ["settings.json", "node_modules", ".git", "*.env", "*.secret"]}