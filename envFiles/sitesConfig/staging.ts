import DefaultSiteConfigs from './qa';

const collectionOnlySiteStores = [
  { barcode: 'https://consumer-app.staging.drpt.io/qr/2000/47865046', name: 'r&d test store' },
];

const collectionAndDeliverySiteStores = [{ barcode: 'XYZ789', name: 'Store 3' }];

// TODO: When starting to work on staging use the actual codes
const SiteConfigs = {
  CollectionOnlySite: {
    ...DefaultSiteConfigs.CollectionOnlySite,
    name: 'Serravalle Designer Outlet',
    packageCodePrefix: 'ITHA',
    stores: collectionOnlySiteStores,
  },

  CollectionAndDeliverySite: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Cheshire Oaks',
    packageCodePrefix: 'UKH',
    stores: collectionAndDeliverySiteStores,
  },

  BusinessDropSite: {
    ...DefaultSiteConfigs.BusinessDropSite,
    name: 'Bicester Village',
    packageCodePrefix: 'CAH',
    stores: [],
  },
};

export default SiteConfigs;
