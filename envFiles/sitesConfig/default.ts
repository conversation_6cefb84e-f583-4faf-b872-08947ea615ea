import Site from '../../testsInfra/entities/site';
import CurrencyCodesEnum from '../../testsInfra/enums/currencyCodesEnum';
import DropTypesEnum from '../../testsInfra/enums/dropTypesEnum';

const CollectionOnlySite: Site = {
  type: 0,
  allowedDropTypes: [DropTypesEnum.Collection],
  name: 'IT Handsfree',
  packageCodePrefix: 'ITHA',
  currencyCode: CurrencyCodesEnum.EURO,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['COLLECTION_POINT'],
  stores: [
    { barcode: 'https://qa.drpt.app/qr/it-handsfree/7fae56ba', name: 'DROPIT TRAINING STORE' },
    { barcode: 'https://qa.drpt.app/qr/it-handsfree/2618ed7e', name: 'Super Mario Pizza' },
  ],
};

const CollectionAndDeliverySite: Site = {
  type: 1,
  allowedDropTypes: [DropTypesEnum.Collection, DropTypesEnum.Delivery],
  name: 'UK Handsfree',
  packageCodePrefix: 'UKH',
  currencyCode: CurrencyCodesEnum.GREAT_BRITISH_POUND,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['HOME', 'COLLECTION_POINT'],
  stores: [{ barcode: 'https://qa.drpt.app/qr/uk-handsfree/3e79f832', name: 'Super Mario Pizza' }],
};

const BusinessDropSite: Site = {
  type: 2,
  allowedDropTypes: [DropTypesEnum.Delivery],
  name: 'CA Smart Standalone',
  packageCodePrefix: 'CASA',
  currencyCode: CurrencyCodesEnum.CANADIAN_DOLLAR,
  featureFlags: [],
  isWithHub: false,
  supportedAddressTypes: ['HOME', 'HOTEL'],
  stores: [{ barcode: 'https://qa.drpt.app/qr/ca-standalone/4c0b13f7', name: 'CA Good Karma Yoga (Wix)' }],
};

export default {
  CollectionOnlySite,
  CollectionAndDeliverySite,
  BusinessDropSite,
};
